"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { DynamicIcon } from "lucide-react/dynamic";
import { sendMessage } from "@/actions/message.action";
import { useParams } from "next/navigation";
import { ChatContent, Message } from "@/typescript/interfaces";

interface MessageInputProps {
  onMessageSent?: (message: Message) => void;
  isCurrentUser: string;
}

const MessageInput = ({ onMessageSent, isCurrentUser }: MessageInputProps) => {
  const [content, setContent] = useState<ChatContent>({
    messageType: "text",
    message: "",
  });
  const [isSending, setIsSending] = useState(false);
  const params = useParams();
  const conversationId = params.conversionId as string;

  const handleSendMessage = async () => {
    if (!content.message.trim()) return;

    try {
      setIsSending(true);
      const response = await sendMessage(conversationId, content);
      console.log("response: ", response);

      if (response.status >= 200 && response.status < 300 && onMessageSent) {
        console.log("message sent successfully: ", response.result);

        // Pass the message directly as returned from the API
        // The API should return the message with the correct user ID format
        onMessageSent(response.result);
      }

      setContent({
        messageType: "text",
        message: "",
      });
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="flex items-center p-4 border-t relative">
      <Input
        type="text"
        value={content.message}
        onChange={(e) => setContent({ ...content, message: e.target.value })}
        onKeyDown={handleKeyDown}
        placeholder="Add a Message..."
        className="flex-1 border rounded-full pl-4 pr-10 py-2 focus:outline-none h-12"
        disabled={isSending}
      />
      <button
        className="text-white bg-secondary p-2 rounded-full cursor-pointer absolute top-6 right-6"
        onClick={handleSendMessage}
        disabled={isSending || !content.message.trim()}
      >
        <DynamicIcon name="send" size={15} color="#fff" />
      </button>
    </div>
  );
};

export default MessageInput;

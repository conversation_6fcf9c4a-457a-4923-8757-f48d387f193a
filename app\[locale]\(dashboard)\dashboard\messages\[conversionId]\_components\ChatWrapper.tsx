"use client";

import React, { useEffect } from "react";
import <PERSON><PERSON><PERSON>ead<PERSON> from "./ChatHeader";
import { ScrollArea } from "@/components/ui/scroll-area";
import MessageGroup from "./ChatGroup";
import MessageInput from "./MessageInput";
import { Message } from "@/typescript/interfaces";
import { useCookies } from "react-cookie";
import { getSocket } from "@/socket/socket";
import { useParams } from "next/navigation";

interface MessageSender {
  name: string;
  avatar: string;
  isCurrentUser: boolean;
}

interface Args {
  currentUser: MessageSender;
  otherUser: MessageSender;
  chats: Message[];
  isCurrentUser: string; // This is the current user's ID
}

const ChatWrapper = ({ currentUser, otherUser, chats: initialChats, isCurrentUser }: Args) => {
  const [cookies] = useCookies(["token"]);
  const params = useParams();
  const token = cookies.token;
  const [chats, setChats] = React.useState<Message[]>(initialChats);
  const conversationId = params?.conversionId as string;
  const messagesEndRef = React.useRef<HTMLDivElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);

  // Debug logging for current user ID
  React.useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("ChatWrapper initialized with:", {
        isCurrentUser,
        currentUserName: currentUser.name,
        otherUserName: otherUser.name,
        totalChats: chats.length,
      });
    }
  }, [isCurrentUser, currentUser.name, otherUser.name, chats.length]);

  // Scroll to bottom when messages change
  useEffect(() => {
    // Instead of scrolling the entire page, scroll only within the ScrollArea
    if (messagesEndRef.current) {
      const scrollContainer = scrollAreaRef.current?.querySelector(
        "[data-radix-scroll-area-viewport]",
      );
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [chats]);

  useEffect(() => {
    if (!token) return; // wait for token

    const socketInstance = getSocket(token);

    socketInstance.on("connect", () => {
      console.log("Connected to chat server ✅");
      socketInstance.emit("join", { conversationId });
    });

    socketInstance.on("message:new", (message: Message) => {
      console.log("New message received:", message);
      setChats((prevChats) => [...prevChats, message]);
    });

    socketInstance.on("error", (error) => {
      console.log("Socket error:", error);
    });

    socketInstance.on("connect_error", (error) => {
      console.log("Connection error:", error);
    });

    return () => {
      socketInstance.off("connect");
      socketInstance.off("message:new");
      socketInstance.off("error");
      socketInstance.off("connect_error");
    };
  }, [token, conversationId]);

  // Sort messages by creation date
  const sortedChats = [...chats].sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  );

  // Handle new messages from socket or message input
  const handleNewMessage = (newMessage: Message) => {
    console.log("new message: ", newMessage);

    // Validate that the message has required fields
    if (!newMessage._id || !newMessage.message) {
      console.error("Invalid message received:", newMessage);
      return;
    }

    // Ensure user field is properly set
    if (!newMessage.user && !newMessage.senderUser) {
      console.warn("Message received without user identification, defaulting to current user");
      newMessage.user = isCurrentUser;
    }

    setChats((prev) => [...prev, newMessage]);
  };

  return (
    <div className="rounded-lg shadow-md bg-white w-full">
      <ChatHeader name={otherUser.name} status="Has Available Space" avatar={otherUser.avatar} />
      <ScrollArea className="h-[480px]" ref={scrollAreaRef}>
        <div className="p-4 space-y-6">
          {sortedChats.map((chat, idx) => {
            // Determine if the message is from the current user
            // According to the Message interface, user field is always a string (user ID)
            // Also check senderUser as fallback if user field is not available
            const messageUserId = chat.user || chat.senderUser;
            const isFromCurrentUser = messageUserId === isCurrentUser;

            // Debug logging to help identify issues
            if (process.env.NODE_ENV === "development") {
              console.log("Message user comparison:", {
                messageUserId,
                isCurrentUser,
                isFromCurrentUser,
                chatUser: chat.user,
                senderUser: chat.senderUser,
              });
            }

            return (
              <MessageGroup
                key={chat._id || idx} // Use message ID as key for better React performance
                sender={isFromCurrentUser ? currentUser : otherUser}
                chat={chat}
              />
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      <MessageInput onMessageSent={handleNewMessage} isCurrentUser={isCurrentUser} />
    </div>
  );
};

export default ChatWrapper;

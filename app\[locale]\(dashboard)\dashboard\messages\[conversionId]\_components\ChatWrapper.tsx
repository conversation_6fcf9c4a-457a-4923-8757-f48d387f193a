"use client";

import React, { useEffect } from "react";
import <PERSON><PERSON><PERSON>ead<PERSON> from "./ChatHeader";
import { ScrollArea } from "@/components/ui/scroll-area";
import MessageGroup from "./ChatGroup";
import MessageInput from "./MessageInput";
import { Message } from "@/typescript/interfaces";
import { useCookies } from "react-cookie";
import { getSocket } from "@/socket/socket";
import { useParams } from "next/navigation";

interface MessageSender {
  name: string;
  avatar: string;
  isCurrentUser: boolean;
}

interface Args {
  currentUser: MessageSender;
  otherUser: MessageSender;
  chats: Message[];
  isCurrentUser: string; // This is the current user's ID
}

const ChatWrapper = ({ currentUser, otherUser, chats: initialChats, isCurrentUser }: Args) => {
  const [cookies] = useCookies(["token"]);
  const params = useParams();
  const token = cookies.token;
  const [chats, setChats] = React.useState<Message[]>(initialChats);
  const conversationId = params?.conversionId as string;
  const messagesEndRef = React.useRef<HTMLDivElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);

  // Debug logging for current user ID
  React.useEffect(() => {
    console.log("🔍 ChatWrapper initialized with:", {
      isCurrentUser,
      isCurrentUserType: typeof isCurrentUser,
      currentUserName: currentUser.name,
      otherUserName: otherUser.name,
      totalChats: chats.length,
    });

    // Log first few messages to see their structure
    if (chats.length > 0) {
      console.log(
        "📝 Sample messages:",
        chats.slice(0, 3).map((chat) => ({
          _id: chat._id,
          user: chat.user,
          userType: typeof chat.user,
          senderUser: chat.senderUser,
          message: chat.message.substring(0, 20) + "...",
        })),
      );
    }
  }, [isCurrentUser, currentUser.name, otherUser.name, chats.length]);

  // Scroll to bottom when messages change
  useEffect(() => {
    // Instead of scrolling the entire page, scroll only within the ScrollArea
    if (messagesEndRef.current) {
      const scrollContainer = scrollAreaRef.current?.querySelector(
        "[data-radix-scroll-area-viewport]",
      );
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [chats]);

  useEffect(() => {
    if (!token) return; // wait for token

    const socketInstance = getSocket(token);

    socketInstance.on("connect", () => {
      console.log("Connected to chat server ✅");
      socketInstance.emit("join", { conversationId });
    });

    socketInstance.on("message:new", (message: Message) => {
      console.log("New message received:", message);
      setChats((prevChats) => [...prevChats, message]);
    });

    socketInstance.on("error", (error) => {
      console.log("Socket error:", error);
    });

    socketInstance.on("connect_error", (error) => {
      console.log("Connection error:", error);
    });

    return () => {
      socketInstance.off("connect");
      socketInstance.off("message:new");
      socketInstance.off("error");
      socketInstance.off("connect_error");
    };
  }, [token, conversationId]);

  // Sort messages by creation date
  const sortedChats = [...chats].sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
  );

  // Handle new messages from socket or message input
  const handleNewMessage = (newMessage: Message) => {
    console.log("new message: ", newMessage);

    // Validate that the message has required fields
    if (!newMessage._id || !newMessage.message) {
      console.error("Invalid message received:", newMessage);
      return;
    }

    // Ensure user field is properly set
    if (!newMessage.user && !newMessage.senderUser) {
      console.warn("Message received without user identification, defaulting to current user");
      newMessage.user = isCurrentUser;
    }

    setChats((prev) => [...prev, newMessage]);
  };

  return (
    <div className="rounded-lg shadow-md bg-white w-full">
      <ChatHeader name={otherUser.name} status="Has Available Space" avatar={otherUser.avatar} />
      <ScrollArea className="h-[480px]" ref={scrollAreaRef}>
        <div className="p-4 space-y-6">
          {sortedChats.map((chat, idx) => {
            // Determine if the message is from the current user
            // According to the Message interface, user field is always a string (user ID)
            // Also check senderUser as fallback if user field is not available
            const messageUserId = chat.user || chat.senderUser;

            // Robust comparison that handles different data types and formats
            let isFromCurrentUser = false;
            if (messageUserId && isCurrentUser) {
              // Convert both to strings for comparison to handle ObjectId vs string issues
              const messageUserStr = String(messageUserId).trim();
              const currentUserStr = String(isCurrentUser).trim();
              isFromCurrentUser = messageUserStr === currentUserStr;
            }

            // Debug logging to help identify issues
            console.log("🔍 Message user comparison:", {
              messageUserId,
              messageUserIdType: typeof messageUserId,
              messageUserStr: String(messageUserId).trim(),
              isCurrentUser,
              isCurrentUserType: typeof isCurrentUser,
              currentUserStr: String(isCurrentUser).trim(),
              isFromCurrentUser,
              chatUser: chat.user,
              senderUser: chat.senderUser,
              strictEquality: messageUserId === isCurrentUser,
              looseEquality: messageUserId == isCurrentUser,
              stringComparison: String(messageUserId).trim() === String(isCurrentUser).trim(),
            });

            return (
              <div key={chat._id || idx}>
                {/* Temporary debug indicator */}
                <div className="text-xs text-gray-400 mb-1">
                  {isFromCurrentUser ? "👤 You" : "👥 Other"} | User: {String(messageUserId)} |
                  Current: {String(isCurrentUser)}
                </div>
                <MessageGroup sender={isFromCurrentUser ? currentUser : otherUser} chat={chat} />
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      <MessageInput onMessageSent={handleNewMessage} isCurrentUser={isCurrentUser} />
    </div>
  );
};

export default ChatWrapper;

import React from "react";
import { cookies } from "next/headers";
import Chat<PERSON>rapper from "./_components/ChatWrapper";
import isAuthenticated from "@/utils/isAuthenticated";
import { getConversationById } from "@/actions/chat.action";

interface Args {
  params: Promise<{
    conversionId: string;
  }>;
}

const baseUrl = process.env.NEXT_PUBLIC_API_URL;

const getChat = async (conversionId: string) => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const response = await fetch(`${baseUrl}/web/conversation/${conversionId}/chat`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    if (!response.ok) {
      throw new Error("Failed to fetch chat");
    }
    const data = await response.json();
    return data;
  } catch (error: unknown) {
    console.log(error);
  }
};

const ChatPage = async ({ params }: Args) => {
  const { conversionId } = await params;
  const conversations = await getConversationById(conversionId);
  const data = await isAuthenticated({ noRedirect: true });
  const chat = await getChat(conversionId);
  const chats = chat.result.records;

  // Ensure we have a valid current user ID
  const isCurrentUser = data?.user?._id;

  if (!isCurrentUser) {
    console.error("❌ No current user ID found:", { data, user: data?.user });
    throw new Error("Unable to identify current user");
  }

  const members = conversations.conversation.members;

  const avatarURL = process.env.NEXT_PUBLIC_CDN_URL;

  const recipient = members.find((member: any) => member.user._id !== isCurrentUser)?.user;

  // Debug logging
  console.log("🔍 ChatPage Debug Info:", {
    isCurrentUser,
    isCurrentUserType: typeof isCurrentUser,
    dataUser: data?.user,
    dataUserId: data?.user?._id,
    members: members.map((m: any) => ({
      id: m.user._id,
      name: m.user.firstName + " " + m.user.lastName,
    })),
    recipient: recipient
      ? { id: recipient._id, name: recipient.firstName + " " + recipient.lastName }
      : null,
  });

  const currentUser = {
    name: data?.user?.firstName + " " + data?.user?.lastName,
    avatar: avatarURL + "/" + data?.user?.avatar?.filePath + "/" + data?.user?.avatar?.fileName,
    isCurrentUser: true,
    intent: data?.user?.intent,
  };
  const otherUser = {
    name: recipient.firstName + " " + recipient.lastName,
    avatar: avatarURL + "/" + recipient?.avatar?.filePath + "/" + recipient?.avatar?.fileName,
    isCurrentUser: false,
    intent: recipient.intent,
  };

  return (
    <ChatWrapper
      currentUser={currentUser}
      otherUser={otherUser}
      chats={chats}
      isCurrentUser={isCurrentUser}
    />
  );
};

export default ChatPage;
